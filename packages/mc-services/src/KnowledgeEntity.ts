/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PaginationResult } from './Base';
import {
  ApplicationBO,
  CodeReviewRuleHitRecordVO,
  CrRuleTestDTO,
  File,
  KnowledgeEntityReq,
  KnowledgeEntityRes,
  KnowledgeSearchRequest,
  LabelValueVO,
  RuleHitModuleRecordVO,
  UpdateCrRuleTestDTO,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface AddCrRuleParams {
  /** identifier */
  identifier: string;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name AddCrRule
 * @summary addCrRule
 * @request POST:/knowledge/entity/addCrRule
 */
export async function addCrRule(
  query: AddCrRuleParams,
  data: CrRuleTestDTO,
  options?: MethodOptions,
): Promise<string[]> {
  return request(`${baseUrl}/knowledge/entity/addCrRule`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface AddDocumentsParams {
  /** identifier */
  identifier: string;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name AddDocuments
 * @summary addDocuments
 * @request POST:/knowledge/entity/addDocuments
 */
export async function addDocuments(
  query: AddDocumentsParams,
  data: Record<string, object>[],
  options?: MethodOptions,
): Promise<string[]> {
  return request(`${baseUrl}/knowledge/entity/addDocuments`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags KnowledgeEntity
 * @name AddDocumentsFromFile
 * @summary 从文件添加知识
 * @request POST:/knowledge/entity/addDocumentsFromFile
 */
export async function addDocumentsFromFile(
  data: {
    /**
     * file
     * @format binary
     */
    file: File;
    /** identifier */
    identifier: string;
  },
  options?: MethodOptions,
): Promise<string[]> {
  return request(`${baseUrl}/knowledge/entity/addDocumentsFromFile`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface DeleteDocumentByIdsParams {
  /** identifier */
  identifier: string;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name DeleteDocumentByIds
 * @summary deleteDocumentByIds
 * @request DELETE:/knowledge/entity/deleteDocumentByIds
 */
export async function deleteDocumentByIds(
  query: DeleteDocumentByIdsParams,
  data: string[],
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/knowledge/entity/deleteDocumentByIds`, {
    method: 'DELETE',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface DeleteDocumentByQueryParams {
  /** identifier */
  identifier: string;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name DeleteDocumentByQuery
 * @summary deleteDocumentByQuery
 * @request DELETE:/knowledge/entity/deleteDocumentByQuery
 */
export async function deleteDocumentByQuery(
  query: DeleteDocumentByQueryParams,
  data: KnowledgeSearchRequest,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/knowledge/entity/deleteDocumentByQuery`, {
    method: 'DELETE',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface FindByIdentifierParams {
  /** identifier */
  identifier: string;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name FindByIdentifier
 * @summary findByIdentifier
 * @request GET:/knowledge/entity/findByIdentifier
 */
export async function findByIdentifier(
  query: FindByIdentifierParams,
  options?: MethodOptions,
): Promise<KnowledgeEntityRes> {
  return request(`${baseUrl}/knowledge/entity/findByIdentifier`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags KnowledgeEntity
 * @name GetCodeReviewRuleEngine
 * @summary getCodeReviewRuleEngine
 * @request GET:/knowledge/entity/getCodeReviewRuleEngine
 */
export async function getCodeReviewRuleEngine(options?: MethodOptions): Promise<LabelValueVO[]> {
  return request(`${baseUrl}/knowledge/entity/getCodeReviewRuleEngine`, {
    method: 'GET',
    ...options,
  });
}

export interface GetCodeReviewRuleHitRecordParams {
  /** documentId */
  documentId: string;
  /** status */
  status?: string;
  /**
   * startTime
   * @format int64
   */
  startTime: number;
  /**
   * endTime
   * @format int64
   */
  endTime: number;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name GetCodeReviewRuleHitRecord
 * @summary getCodeReviewRuleHitRecord
 * @request GET:/knowledge/entity/getCodeReviewRuleHitRecord
 */
export async function getCodeReviewRuleHitRecord(
  query: GetCodeReviewRuleHitRecordParams,
  options?: MethodOptions,
): Promise<CodeReviewRuleHitRecordVO> {
  return request(`${baseUrl}/knowledge/entity/getCodeReviewRuleHitRecord`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags KnowledgeEntity
 * @name GetCodeReviewRuleLanguage
 * @summary getCodeReviewRuleLanguage
 * @request GET:/knowledge/entity/getCodeReviewRuleLanguage
 */
export async function getCodeReviewRuleLanguage(options?: MethodOptions): Promise<LabelValueVO[]> {
  return request(`${baseUrl}/knowledge/entity/getCodeReviewRuleLanguage`, {
    method: 'GET',
    ...options,
  });
}

/**
 * No description
 * @tags KnowledgeEntity
 * @name GetCodeReviewRuleStatus
 * @summary getCodeReviewRuleStatus
 * @request GET:/knowledge/entity/getCodeReviewRuleStatus
 */
export async function getCodeReviewRuleStatus(options?: MethodOptions): Promise<LabelValueVO[]> {
  return request(`${baseUrl}/knowledge/entity/getCodeReviewRuleStatus`, {
    method: 'GET',
    ...options,
  });
}

/**
 * No description
 * @tags KnowledgeEntity
 * @name GetCodeReviewRuleType
 * @summary getCodeReviewRuleType
 * @request GET:/knowledge/entity/getCodeReviewRuleType
 */
export async function getCodeReviewRuleType(options?: MethodOptions): Promise<LabelValueVO[]> {
  return request(`${baseUrl}/knowledge/entity/getCodeReviewRuleType`, {
    method: 'GET',
    ...options,
  });
}

export interface GetCodeRuleOptimizationParams {
  /** originalRule */
  originalRule: string;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name GetCodeRuleOptimization
 * @summary getCodeRuleOptimization
 * @request GET:/knowledge/entity/getCodeRuleOptimization
 */
export async function getCodeRuleOptimization(
  query: GetCodeRuleOptimizationParams,
  options?: MethodOptions,
): Promise<string> {
  return request(`${baseUrl}/knowledge/entity/getCodeRuleOptimization`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRuleCrHitModuleRecordParams {
  /** documentId */
  documentId: string;
  /**
   * moduleId
   * @format int64
   */
  moduleId?: number;
  /**
   * pageNum
   * @format int32
   */
  pageNum: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize: number;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name GetRuleCrHitModuleRecord
 * @summary getRuleCrHitModuleRecord
 * @request GET:/knowledge/entity/getRuleCrHitModuleRecord
 */
export async function getRuleCrHitModuleRecord(
  query: GetRuleCrHitModuleRecordParams,
  options?: MethodOptions,
): Promise<PaginationResult<RuleHitModuleRecordVO>> {
  return request(`${baseUrl}/knowledge/entity/getRuleCrHitModuleRecord`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRuleCrHitModulesParams {
  /** documentId */
  documentId: string;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name GetRuleCrHitModules
 * @summary getRuleCrHitModules
 * @request GET:/knowledge/entity/getRuleCrHitModules
 */
export async function getRuleCrHitModules(
  query: GetRuleCrHitModulesParams,
  options?: MethodOptions,
): Promise<ApplicationBO[]> {
  return request(`${baseUrl}/knowledge/entity/getRuleCrHitModules`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRuleGateHitModuleRecordParams {
  /** documentId */
  documentId: string;
  /**
   * moduleId
   * @format int64
   */
  moduleId?: number;
  /**
   * pageNum
   * @format int32
   */
  pageNum: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize: number;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name GetRuleGateHitModuleRecord
 * @summary getRuleGateHitModuleRecord
 * @request GET:/knowledge/entity/getRuleGateHitModuleRecord
 */
export async function getRuleGateHitModuleRecord(
  query: GetRuleGateHitModuleRecordParams,
  options?: MethodOptions,
): Promise<PaginationResult<RuleHitModuleRecordVO>> {
  return request(`${baseUrl}/knowledge/entity/getRuleGateHitModuleRecord`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRuleGateHitModulesParams {
  /** documentId */
  documentId: string;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name GetRuleGateHitModules
 * @summary getRuleGateHitModules
 * @request GET:/knowledge/entity/getRuleGateHitModules
 */
export async function getRuleGateHitModules(
  query: GetRuleGateHitModulesParams,
  options?: MethodOptions,
): Promise<ApplicationBO[]> {
  return request(`${baseUrl}/knowledge/entity/getRuleGateHitModules`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags KnowledgeEntity
 * @name RegisterKnowledgeEntity
 * @summary registerKnowledgeEntity
 * @request POST:/knowledge/entity/registerKnowledgeEntity
 */
export async function registerKnowledgeEntity(data: KnowledgeEntityReq, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/knowledge/entity/registerKnowledgeEntity`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface UpdateCrRuleParams {
  /** identifier */
  identifier: string;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name UpdateCrRule
 * @summary updateCrRule
 * @request PUT:/knowledge/entity/updateCrRule
 */
export async function updateCrRule(
  query: UpdateCrRuleParams,
  data: UpdateCrRuleTestDTO,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/knowledge/entity/updateCrRule`, {
    method: 'PUT',
    params: query,
    body: data as any,
    ...options,
  });
}

export interface UpdateDocumentParams {
  /** identifier */
  identifier: string;
}
/**
 * No description
 * @tags KnowledgeEntity
 * @name UpdateDocument
 * @summary updateDocument
 * @request PUT:/knowledge/entity/updateDocument
 */
export async function updateDocument(
  query: UpdateDocumentParams,
  data: Record<string, object>,
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/knowledge/entity/updateDocument`, {
    method: 'PUT',
    params: query,
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags KnowledgeEntity
 * @name UpdateKnowledgeEntity
 * @summary updateKnowledgeEntity
 * @request POST:/knowledge/entity/updateKnowledgeEntity
 */
export async function updateKnowledgeEntity(data: KnowledgeEntityReq, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/knowledge/entity/updateKnowledgeEntity`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}
