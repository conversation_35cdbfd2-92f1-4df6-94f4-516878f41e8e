/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PaginationResult } from './Base';
import { CheckResult, DateInfo, VersionPlanBO } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface CheckVersionPlanEarlyParams {
  /**
   * versionPlanId
   * @format int64
   */
  versionPlanId: number;
}
/**
 * No description
 * @tags VersionPlan
 * @name CheckVersionPlanEarly
 * @summary 判断当前版本计划的选择 是否超前，仅限于持续集成交付模式
 * @request GET:/api/v1/versionPlan/checkVersionPlanEarly
 */
export async function checkVersionPlanEarly(
  query: CheckVersionPlanEarlyParams,
  options?: MethodOptions,
): Promise<CheckResult> {
  return request(`${baseUrl}/api/v1/versionPlan/checkVersionPlanEarly`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetDateInfoMapByMonthParams {
  /** month */
  month: string;
}
/**
 * No description
 * @tags VersionPlan
 * @name GetDateInfoMapByMonth
 * @summary 根据月份查询日期信息，会返回前后总共三个月的信息
 * @request GET:/api/v1/versionPlan/getDateInfoMapByMonth
 */
export async function getDateInfoMapByMonth(
  query: GetDateInfoMapByMonthParams,
  options?: MethodOptions,
): Promise<Record<string, DateInfo>> {
  return request(`${baseUrl}/api/v1/versionPlan/getDateInfoMapByMonth`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface QueryVersionPlanPageParams {
  /** keyword */
  keyword?: string;
  /**
   * pageNum
   * @format int32
   */
  pageNum?: number;
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
}
/**
 * No description
 * @tags VersionPlan
 * @name QueryVersionPlanPage
 * @summary 分页查询应用的版本计划
 * @request GET:/api/v1/versionPlan/query
 */
export async function queryVersionPlanPage(
  query?: QueryVersionPlanPageParams,
  options?: MethodOptions,
): Promise<PaginationResult<VersionPlanBO>> {
  return request(`${baseUrl}/api/v1/versionPlan/query`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetVersionPlanParams {
  /**
   * id
   * @format int64
   */
  id: number;
}
/**
 * No description
 * @tags VersionPlan
 * @name GetVersionPlan
 * @summary 根据主键查询版本计划
 * @request GET:/api/v1/versionPlan/queryById
 */
export async function getVersionPlan(query: GetVersionPlanParams, options?: MethodOptions): Promise<VersionPlanBO> {
  return request(`${baseUrl}/api/v1/versionPlan/queryById`, {
    method: 'GET',
    params: query,
    ...options,
  });
}
