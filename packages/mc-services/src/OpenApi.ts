/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { PaginationResult } from './Base';
import { OpenApi, OpenApiMultiQuery, OpenApiSingleQuery, OpenBizParam } from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface DeleteApiParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** type */
  type: 'HSF' | 'HTTP';
}
/**
 * No description
 * @tags OpenApi
 * @name DeleteApi
 * @summary 根据id删除OpenApi
 * @request POST:/api/v1/open/api/delete
 */
export async function deleteApi(query: DeleteApiParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/open/api/delete`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface DoDeleteParams {
  /**
   * id
   * @format int64
   */
  id: number;
  /** type */
  type: 'HSF' | 'HTTP';
  /**
   * subId
   * @format int64
   */
  subId: number;
}
/**
 * No description
 * @tags OpenApi
 * @name DoDelete
 * @summary delete
 * @request POST:/api/v1/open/api/doDelete
 */
export async function doDelete(query: DoDeleteParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/open/api/doDelete`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface FindParams {
  /**
   * pageSize
   * @format int32
   */
  pageSize?: number;
  /**
   * pageNum
   * @format int32
   */
  pageNum?: number;
}
/**
 * No description
 * @tags OpenApi
 * @name Find
 * @summary 分页查询OpenApi
 * @request POST:/api/v1/open/api/find
 */
export async function find(
  data: OpenApiMultiQuery,
  query?: FindParams,
  options?: MethodOptions,
): Promise<PaginationResult<OpenApi>> {
  return request(`${baseUrl}/api/v1/open/api/find`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags OpenApi
 * @name FindAll
 * @summary 查询所有OpenApi
 * @request POST:/api/v1/open/api/findAll
 */
export async function findAll(data: OpenApiMultiQuery, options?: MethodOptions): Promise<OpenApi[]> {
  return request(`${baseUrl}/api/v1/open/api/findAll`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags OpenApi
 * @name FindSingle
 * @summary 查询单个OpenApi
 * @request POST:/api/v1/open/api/findSingle
 */
export async function findSingle(data: OpenApiSingleQuery, options?: MethodOptions): Promise<OpenApi> {
  return request(`${baseUrl}/api/v1/open/api/findSingle`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags OpenApi
 * @name Register
 * @summary 注册OpenApi
 * @request POST:/api/v1/open/api/register
 */
export async function register(data: OpenApi, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/open/api/register`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

export interface SyncHttpOpenApisParams {
  /**
   * providerId
   * @format int64
   */
  providerId: number;
}
/**
 * No description
 * @tags OpenApi
 * @name SyncHttpOpenApis
 * @summary 同步OpenApi
 * @request POST:/api/v1/open/api/syncHttpOpenApis
 */
export async function syncHttpOpenApis(query: SyncHttpOpenApisParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/open/api/syncHttpOpenApis`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface UpdateBizParamsParams {
  /**
   * apiId
   * @format int64
   */
  apiId: number;
}
/**
 * No description
 * @tags OpenApi
 * @name UpdateBizParams
 * @summary 更新api业务属性信息
 * @request POST:/api/v1/open/api/updateBizParams
 */
export async function updateBizParams(
  query: UpdateBizParamsParams,
  data: OpenBizParam[],
  options?: MethodOptions,
): Promise<boolean> {
  return request(`${baseUrl}/api/v1/open/api/updateBizParams`, {
    method: 'POST',
    params: query,
    body: data as any,
    ...options,
  });
}
