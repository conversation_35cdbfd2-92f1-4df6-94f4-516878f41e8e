/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  AccessToken,
  AgentAccessTokenVO,
  AgentAddRequest,
  AgentConfigVO,
  AgentInstanceBO,
  AgentInstanceOfflineRequest,
  AgentInstanceOnlineRequest,
  AgentInstanceVO,
  AgentListRequest,
  AgentOfflineRequest,
  AgentOnlineRequest,
  AgentPageRequest,
  AgentPublishRequest,
  AgentRerankModelVO,
  AgentTokenAddRequest,
  AgentTokenVO,
  AgentUpdateConfigRequest,
  AgentUpdateRequest,
  AgentVO,
  LlmModelVO,
  PageAgentVO,
} from './AiMiDataContracts';
import { getBaseUrl, MethodOptions } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://aimi.alibaba-inc.com');

export interface QueryAgentParams {
  /** @format int64 */
  agentId: number;
}
/**
 * No description
 * @tags AiMiAgent
 * @name QueryAgent
 * @summary 根据ID查询Agent详情
 * @request GET:/aimi/api/v1/agent
 */
export async function queryAgent(query: QueryAgentParams, options?: MethodOptions): Promise<AgentVO> {
  return request(`${baseUrl}/aimi/api/v1/agent`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name UpdateAgent
 * @summary 更新Agent基础信息
 * @request PUT:/aimi/api/v1/agent
 */
export async function updateAgent(data: AgentUpdateRequest, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/agent`, {
    method: 'PUT',
    body: data as any,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name AddAgent
 * @summary 添加Agent
 * @request POST:/aimi/api/v1/agent
 */
export async function addAgent(data: AgentAddRequest, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/agent`, {
    method: 'POST',
    body: data as any,
    ...options,
    suffix: false,
  });
}

export interface OnlineVersionParams {
  request: AgentInstanceOnlineRequest;
}
/**
 * No description
 * @tags AiMiAgent
 * @name OnlineVersion
 * @summary 重新上架Agent版本
 * @request PUT:/aimi/api/v1/agent/version/online
 */
export async function onlineVersion(query: OnlineVersionParams, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/agent/version/online`, {
    method: 'PUT',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface OfflineVersionParams {
  request: AgentInstanceOfflineRequest;
}
/**
 * No description
 * @tags AiMiAgent
 * @name OfflineVersion
 * @summary 下架Agent版本
 * @request PUT:/aimi/api/v1/agent/version/offline
 */
export async function offlineVersion(query: OfflineVersionParams, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/agent/version/offline`, {
    method: 'PUT',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface OnlineParams {
  request: AgentOnlineRequest;
}
/**
 * No description
 * @tags AiMiAgent
 * @name Online
 * @summary 重新上架Agent
 * @request PUT:/aimi/api/v1/agent/online
 */
export async function online(query: OnlineParams, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/agent/online`, {
    method: 'PUT',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface OfflineParams {
  request: AgentOfflineRequest;
}
/**
 * No description
 * @tags AiMiAgent
 * @name Offline
 * @summary 下架Agent
 * @request PUT:/aimi/api/v1/agent/offline
 */
export async function offline(query: OfflineParams, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/agent/offline`, {
    method: 'PUT',
    params: query,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name UpdateAgentConfig
 * @summary 更新Agent配置
 * @request PUT:/aimi/api/v1/agent/config
 */
export async function updateAgentConfig(data: AgentUpdateConfigRequest, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/agent/config`, {
    method: 'PUT',
    body: data as any,
    ...options,
    suffix: false,
  });
}

export interface QueryAgentInstanceParams {
  /** @format int64 */
  agentInstanceId: number;
}
/**
 * No description
 * @tags AiMiAgent
 * @name QueryAgentInstance
 * @summary 根据ID查询Agent版本
 * @request GET:/aimi/api/v1/agent/version
 */
export async function queryAgentInstance(
  query: QueryAgentInstanceParams,
  options?: MethodOptions,
): Promise<AgentInstanceVO> {
  return request(`${baseUrl}/aimi/api/v1/agent/version`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name PublishVersion
 * @summary 发布Agent版本
 * @request POST:/aimi/api/v1/agent/version
 */
export async function publishVersion(data: AgentPublishRequest, options?: MethodOptions): Promise<AgentInstanceVO> {
  return request(`${baseUrl}/aimi/api/v1/agent/version`, {
    method: 'POST',
    body: data as any,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name QueryAgentToken
 * @summary 查询AgentToken
 * @request GET:/aimi/api/v1/agent/token
 */
export async function queryAgentToken(options?: MethodOptions): Promise<AgentTokenVO> {
  return request(`${baseUrl}/aimi/api/v1/agent/token`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name AddAgentToken
 * @summary 添加AgentToken
 * @request POST:/aimi/api/v1/agent/token
 */
export async function addAgentToken(data: AgentTokenAddRequest, options?: MethodOptions): Promise<AgentTokenVO> {
  return request(`${baseUrl}/aimi/api/v1/agent/token`, {
    method: 'POST',
    body: data as any,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name RenderInspectorConfig
 * @summary 渲染实例配置
 * @request POST:/aimi/api/v1/agent/renderInspectorConfig
 */
export async function renderInspectorConfig(data: AgentVO, options?: MethodOptions): Promise<AgentInstanceBO> {
  return request(`${baseUrl}/aimi/api/v1/agent/renderInspectorConfig`, {
    method: 'POST',
    body: data as any,
    ...options,
    suffix: false,
  });
}

export interface ListAgentVersionsParams {
  /** @format int64 */
  agentId: number;
}
/**
 * No description
 * @tags AiMiAgent
 * @name ListAgentVersions
 * @summary 查询Agent版本列表
 * @request GET:/aimi/api/v1/agent/version/list
 */
export async function listAgentVersions(
  query: ListAgentVersionsParams,
  options?: MethodOptions,
): Promise<AgentInstanceVO[]> {
  return request(`${baseUrl}/aimi/api/v1/agent/version/list`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name QueryAgentSsoTicket
 * @summary 查询AgentSsoTicket
 * @request GET:/aimi/api/v1/agent/ssoTicket
 */
export async function queryAgentSsoTicket(options?: MethodOptions): Promise<string> {
  return request(`${baseUrl}/aimi/api/v1/agent/ssoTicket`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name ListAgentRerankModels
 * @summary 查询Rerank模型列表
 * @request GET:/aimi/api/v1/agent/rerankModel/list
 */
export async function listAgentRerankModels(options?: MethodOptions): Promise<AgentRerankModelVO[]> {
  return request(`${baseUrl}/aimi/api/v1/agent/rerankModel/list`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

export interface PageAgentParams {
  request: AgentPageRequest;
}
/**
 * No description
 * @tags AiMiAgent
 * @name PageAgent
 * @summary 分页查询Agent列表
 * @request GET:/aimi/api/v1/agent/page
 */
export async function pageAgent(query: PageAgentParams, options?: MethodOptions): Promise<PageAgentVO> {
  return request(`${baseUrl}/aimi/api/v1/agent/page`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name ListAgentLlmModels
 * @summary 查询大模型列表
 * @request GET:/aimi/api/v1/agent/llmModel/list
 */
export async function listAgentLlmModels(options?: MethodOptions): Promise<LlmModelVO[]> {
  return request(`${baseUrl}/aimi/api/v1/agent/llmModel/list`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

export interface ListAgentParams {
  request: AgentListRequest;
}
/**
 * No description
 * @tags AiMiAgent
 * @name ListAgent
 * @summary 查询Agent列表
 * @request GET:/aimi/api/v1/agent/list
 */
export async function listAgent(query: ListAgentParams, options?: MethodOptions): Promise<AgentVO[]> {
  return request(`${baseUrl}/aimi/api/v1/agent/list`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface QueryAgentByIdentifierParams {
  agentIdentifier: string;
}
/**
 * No description
 * @tags AiMiAgent
 * @name QueryAgentByIdentifier
 * @summary 根据Identifier查询Agent详情
 * @request GET:/aimi/api/v1/agent/identifier
 */
export async function queryAgentByIdentifier(
  query: QueryAgentByIdentifierParams,
  options?: MethodOptions,
): Promise<AgentVO> {
  return request(`${baseUrl}/aimi/api/v1/agent/identifier`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiAgent
 * @name GetDefaultConfig1
 * @summary Agent默认配置
 * @request GET:/aimi/api/v1/agent/defaultConfig
 */
export async function getDefaultConfig1(options?: MethodOptions): Promise<AgentConfigVO> {
  return request(`${baseUrl}/aimi/api/v1/agent/defaultConfig`, {
    method: 'GET',
    ...options,
    suffix: false,
  });
}

export interface QueryAgentAccessTokenParams {
  /** @format int64 */
  agentId: number;
}
/**
 * No description
 * @tags AiMiAgent
 * @name QueryAgentAccessToken
 * @summary 查询Agent Access Token
 * @request GET:/aimi/api/v1/agent/accessToken
 */
export async function queryAgentAccessToken(
  query: QueryAgentAccessTokenParams,
  options?: MethodOptions,
): Promise<AgentAccessTokenVO> {
  return request(`${baseUrl}/aimi/api/v1/agent/accessToken`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface GetAgentAccessTokenDetailParams {
  accessToken: string;
}
/**
 * No description
 * @tags AiMiAgent
 * @name GetAgentAccessTokenDetail
 * @summary 查询Access Token
 * @request GET:/aimi/api/v1/agent/accessToken/detail
 */
export async function getAgentAccessTokenDetail(
  query: GetAgentAccessTokenDetailParams,
  options?: MethodOptions,
): Promise<AccessToken> {
  return request(`${baseUrl}/aimi/api/v1/agent/accessToken/detail`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}
