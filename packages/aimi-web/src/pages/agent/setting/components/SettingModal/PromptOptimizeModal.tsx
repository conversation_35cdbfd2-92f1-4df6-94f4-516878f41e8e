import React, { useState } from 'react';
import { Flex, Modal, type ModalProps } from 'antd';
import AimiDialog from '@/components/AimiDialog';
import { Conversation } from '@/types/dialog';
import styles from './index.module.less';

const DEFAULT_DIALOG: Conversation[] = [
  {
    id: 'default',
    answer: {
      content: '基于您现在的提示词，我将为你优化。',
    },
  },
];

type PromptOptimizeModalProps = {
  onClose?: () => void;
} & ModalProps;

function PromptOptimizeModal(props: PromptOptimizeModalProps) {
  const { onClose, ...restProps } = props;
  const [dialogData, setDialogData] = useState(DEFAULT_DIALOG);

  const handleOk = () => {
    onClose?.();
  };

  const sendMessage = (message?: string) => {
    setDialogData((prevDialog) =>
      prevDialog.concat({
        id: `${Date.now()}`,
        question: {
          content: message,
        },
        answer: {
          content: '我在思考...',
        },
      }),
    );
  };

  return (
    <Modal
      width={750}
      title={<Flex className={styles.modalTitle}>提示词优化</Flex>}
      onOk={handleOk}
      onCancel={onClose}
      {...restProps}
    >
      <AimiDialog
        style={{ minHeight: 500 }}
        data={dialogData}
        sender={{
          showToolbar: true,
          autoSize: { minRows: 1, maxRows: 1 },
          placeholder: '和机器人聊天💬',
          onSubmit: sendMessage,
        }}
      />
    </Modal>
  );
}

export default React.memo(PromptOptimizeModal);
