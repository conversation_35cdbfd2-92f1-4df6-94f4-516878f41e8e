import { CodescanOutlined, InfinityOutlined, LoopOutlined, RocketOutlined, UniappOutlined } from '@ali/mc-icons';
import React from 'react';

type IterationScopeIconProps = {
  identifier: string;
  // mainEntityType: string;
};
export default function IterationScopeIcon(props: IterationScopeIconProps) {
  const { identifier } = props;

  switch (identifier) {
    case 'BIZ_DYNAMIC_PUBLISH':
      return <RocketOutlined />;
    case 'APP_SELF_PUBLISH':
      return <CodescanOutlined />;
    case 'SDK_AGILE_CI_INTEGRATION':
    case 'SDK_VERIFY_INTEGRATION':
    case 'KMP_DEVELOPMENT':
      return <LoopOutlined />;
    case 'BIZ_DYNAMIC_DEVELOPMENT':
      return <RocketOutlined />;
    case 'UNI_APP_DEVELOPMENT':
    case 'CROSS_PLATFORM_DEVELOPMENT':
      return <UniappOutlined />;
    default:
      return <InfinityOutlined />;
  }
}
