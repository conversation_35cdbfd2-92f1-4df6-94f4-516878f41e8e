import { Flex, Input, message } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styles from './iterationDetail.module.less';
import { useIterationContext } from '@/components/Iteration/IterationProvider';

import { useRequest } from '@ali/mc-request';
import {
  GetChangeRecordDefaultTargetBranchParams,
  GetMainFrameworkChangeRecordsParams,
  batchAddAlterSheetModule,
  getChangeRecordDefaultTargetBranch,
  getMainFrameworkChangeRecords,
  syncIntegratedReqRelations,
  SyncIntegratedReqRelationsParams,
} from '@ali/mc-services/AlterSheet';
import { SearchOutlined } from '@ali/mc-icons';
import { AlterSheetModuleVO, AlterSheetModuleBatchCreateDTO, MainFrameworkChangeRecord } from '@ali/mc-services';

import ImportIntegrationModule from '../ImportIntegrationModule';
// import CLIPopover from '@/components/CLIPopover';
import { camelCase } from 'lodash-es';
import ModuleListTitle from '../DevStageModule/ModuleListTitle';
import ModuleList from '../DevStageModule/ModuleList';
import AddMainFrameworkModal from '../AddMainFramework';
import MainFrameworkList from '../DevStageModule/MainFrameworkList';
import { useDevStageBuildContext } from '../DevStageBuild/DevStageBuildProvider';
import { useDevSpaceNonIntegrateSdkDeployContext } from './DevSpaceNonIntegrateSdkDeployProvider';
import { getMixedScope } from './DevStageBuild';
import ModuleModal from '@/components/ModuleModal';
import { useIterationWorkflowContext } from '../../common/IterationWorkflowProvider';


const isFinished = (status: string) => status === 'PUBLISHED' || status === 'CLOSED';
const NewDevStageModule = () => {
  const { data: iterationVO = {}, run: requestImmediately } = useIterationContext();
  const { requestPipelineList: requestBuildPipelineList } = useDevStageBuildContext();
  const { requestPipelineList: requestDeployPipelineList } = useDevSpaceNonIntegrateSdkDeployContext();
  const { workflowValue } = useIterationWorkflowContext();
  const { mainEntity, workflow = {} } = iterationVO;
  const {
    id: alterSheetId,
    alterSheetModuleList,
    application,
    versionPlanId,
    status,
    type: alterSheetType,
  } = mainEntity || ({} as any);
  const { scope } = workflow;
  const pageType = camelCase(workflow?.workflowScope?.identifier?.toLowerCase());
  const disabled = pageType !== 'sdkVerifyIntegration' && isFinished(status);
  const [searchWord, setSearchWord] = useState<string>('');
  const [tab, setTab] = useState<'module' | 'mainFramework'>('module');
  const [targetBranch, setTargetBranch] = useState<string | null>(null);
  const [mainFrameworkList, setMainFrameworkList] = useState<MainFrameworkChangeRecord[]>([]);
  const { runAsync: requestBatchAddAlterSheetModule } = useRequest<number[], [AlterSheetModuleBatchCreateDTO]>(
    batchAddAlterSheetModule,
  );
  const { runAsync: syncIntegratedReqRelation } = useRequest<boolean, [SyncIntegratedReqRelationsParams]>(
    syncIntegratedReqRelations,
  );

  let buildScope = 'ALTER_SHEET_BUILD';
  if (alterSheetType === 'AGILE_CI') {
    buildScope = 'AGILE_CI_ALTER_SHEET_BUILD';
  } else if (alterSheetType === 'NATIVE_DYNAMIC') {
    buildScope = 'NATIVE_DYNAMIC_ALTER_SHEET_BUILD';
  }
  const mixedScope = getMixedScope({ scope: buildScope, alterSheetType });

  const refreshIterationAndPipelineList = useCallback(() => {
    requestImmediately?.();
    requestBuildPipelineList({
      id: alterSheetId,
      pageSize: 100,
      scope: mixedScope,
      onlyShowMine: false,
    });
    requestDeployPipelineList({
      id: alterSheetId,
      pipelineStatus: 'ACTIVE',
      pageSize: 100,
      scope: 'ALTER_SHEET_DEPLOY',
      onlyShowMine: false,
    });
  }, [requestImmediately, requestBuildPipelineList, requestDeployPipelineList, alterSheetId, mixedScope]);

  const importModule = async (modules: AlterSheetModuleVO[], integrateAreaId: number) => {
    const createDTOs = (modules ?? []).map((item) => {
      return {
        alterMode: item.alterMode,
        alterSheetId,
        alterType: 'BINARY',
        moduleId: item.moduleId,
        version: item?.integrateVersion,
        submodules: item?.submodules,
      };
    }) as any;

    const [batchAddAlterSheetModuleResult, syncIntegratedReqRelationResult] = await Promise.allSettled([requestBatchAddAlterSheetModule({
      alterSheetId,
      createDTOs,
    }), syncIntegratedReqRelation({
      alterSheetId,
      integrateAreaId,
    })]);

    // 这边一键同步如果本身就包含了某个已有模块 然后就报错了，但是同步需求关系同步成功这也需要刷新 虽然不影响但是整个mc会有报错提示,清黎：这个是业务方操作的时候要去保证的
    if (batchAddAlterSheetModuleResult?.value || syncIntegratedReqRelationResult?.value) {
      message.success('一键同步成功');
      refreshIterationAndPipelineList?.();
    }
  };

  const { runAsync: requestMainFrameworkChangeRecords, loading: mainFrameworkLoading } = useRequest<
    MainFrameworkChangeRecord[],
    [GetMainFrameworkChangeRecordsParams]
  >(getMainFrameworkChangeRecords);

  const getMainFrameworkList = useCallback(() => {
    if (alterSheetId) {
      requestMainFrameworkChangeRecords({
        alterSheetId,
      }).then((res: MainFrameworkChangeRecord[]) => {
        if (res) {
          setMainFrameworkList(
            res?.map((item) => {
              return {
                ...item,
                key: item.id,
                name: '壳工程',
              };
            }),
          );
        }
      });
    }
  }, [requestMainFrameworkChangeRecords, alterSheetId]);
  const { runAsync: requestChangeRecordDefaultTargetBranch } = useRequest<
    string,
    [GetChangeRecordDefaultTargetBranchParams]
  >(getChangeRecordDefaultTargetBranch);

  const getTargetBranch = useCallback((id: number) => {
    if (id) {
      requestChangeRecordDefaultTargetBranch({ versionPlanId: id }).then((res: string) => {
        if (res) {
          setTargetBranch(res);
        }
      });
    }
  }, [requestChangeRecordDefaultTargetBranch]);

  useEffect(() => {
    if (scope && ['SDK_AGILE_CI_INTEGRATION', 'SDK_VERIFY_INTEGRATION'].includes(scope)) {
      getMainFrameworkList();
    }
  }, [scope, getMainFrameworkList]);

  useEffect(() => {
    if (scope === 'SDK_AGILE_CI_INTEGRATION' && versionPlanId) {
      getTargetBranch(versionPlanId);
    }
  }, [versionPlanId, scope, getTargetBranch]);


  const finalModuleList = useMemo(() => {
    let result = [];
    if (searchWord?.length) {
      result = (alterSheetModuleList ?? []).filter(item => item?.module?.name?.indexOf(searchWord) > -1);
    } else {
      result = alterSheetModuleList ?? [];
    }
    return result;
  }, [alterSheetModuleList, searchWord]);
  const mainModuleList = (alterSheetModuleList ?? []).filter((item: AlterSheetModuleVO) => !item.mainModuleId);
  const mainModuleCount = mainModuleList?.length;
  const sourceModuleCount = (mainModuleList ?? []).filter((item: any) => item.alterType === 'SOURCE').length;
  const binaryModuleCount = (mainModuleList ?? []).filter((item: any) => item.alterType === 'BINARY').length;

  return (<Flex flex="1" vertical className={styles.devStageModuleContent}>
    <Flex className={styles.actionArea} justify="space-between" align="center">
      {tab === 'module' ? <Input
        style={{ width: 574 }}
        value={searchWord}
        allowClear
        onChange={(e) => {
          setSearchWord(e.target.value);
        }}
        placeholder="模块名称"
        prefix={<SearchOutlined />}
      /> : <div />}
      <Flex>
        {tab === 'module' && <>
          {/* <Flex style={{ marginInlineEnd: token.marginXS }}>
            <CLIPopover type="module" />
          </Flex> */}
          {scope === 'SDK_AGILE_CI_INTEGRATION' && (
            <ImportIntegrationModule disabled={disabled} appId={application.id} importModule={importModule} />
          )}
          <ModuleModal
            type="create"
            disabled={disabled}
            refresh={refreshIterationAndPipelineList}
            iterationVO={iterationVO}
            stepId={workflowValue?.flowId}
            scope={scope}
          />
        </>}
        {
          tab === 'mainFramework' && (
            <AddMainFrameworkModal
              disabled={disabled}
              refresh={getMainFrameworkList}
              defaultValue={{
                appId: application.id,
                alterSheetId,
                scmAddress: application?.codeLibraryAddress,
              }}
              targetBranch={targetBranch}
              versionPlanId={versionPlanId}
              path={scope || ''}
            />)
        }
      </Flex>
    </Flex>
    <Flex className={styles.moduleListWrap} vertical>
      <ModuleListTitle
        tab={tab}
        setTab={setTab}
        mainModuleCount={mainModuleCount}
        binaryModuleCount={binaryModuleCount}
        sourceModuleCount={sourceModuleCount}
        mainFrameworkListCount={mainFrameworkList.length}
        scope={scope || ''}
      />
      {tab === 'module' &&
        <ModuleList
          dataSource={finalModuleList}
          refresh={refreshIterationAndPipelineList}
        />}
      {tab === 'mainFramework' && (
        <MainFrameworkList
          data={mainFrameworkList}
          loading={mainFrameworkLoading}
          getMainFrameworkList={getMainFrameworkList}
          targetBranch={targetBranch}
          scope={scope || ''}
        />)}
    </Flex>
  </Flex>);
};
export default NewDevStageModule;
