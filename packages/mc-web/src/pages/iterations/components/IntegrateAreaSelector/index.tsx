import { useRequest } from '@ali/mc-request';
import { IntegrateAreaBO, PaginationResult } from '@ali/mc-services';
import { findIntegrateAreaList, FindIntegrateAreaListParams } from '@ali/mc-services/IntegrationArea';
import { PrefixSelect } from '@ali/mc-uikit';
import React, { useEffect, useMemo } from 'react';
interface IntegrateAreaSelectorProps {
  appId: number;
  typeList?: string;
  pageType?: string;
  onChange?: (value: number) => void;
  value?: number;
}
const IntegrateAreaSelector = ({
  appId,
  typeList,
  pageType,
  value,
  onChange,
}: IntegrateAreaSelectorProps) => {
  const {
    data: integrateAreaList,
    runAsync: requestIntegrateAreaList,
    loading,
  } = useRequest<
  PaginationResult<IntegrateAreaBO>,
  [FindIntegrateAreaListParams | undefined]
  >(findIntegrateAreaList);

  useEffect(() => {
    if (appId) {
    requestIntegrateAreaList({
      applicationId: appId,
      typeList: typeList as any,
      excludedStatus: 'CLOSED',
      inVersionPlan: pageType === 'sdkVerifyIntegration' ? false : undefined,
    });
}
  }, [appId, typeList, pageType]);

  const options = useMemo(() => {
    return (integrateAreaList?.items || []).map((item) => ({
      label: item?.name,
      value: item?.id,
    }));
  }, [integrateAreaList]);

  return (<PrefixSelect
    options={options}
    loading={loading}
    value={value}
    onChange={(id) => {
      onChange?.(id);
    }}
  />);
};

export default IntegrateAreaSelector;
