
import { Button } from 'antd';
import React, { useState } from 'react';
import ModalContent from './ModalContent';
import { AddMainFrameworkModalProps } from './type';


const AddMainFrameworkModal = ({ title, disabled, ...restProps }: AddMainFrameworkModalProps) => {
  const [open, setOpen] = useState<boolean>(false);
  const handleOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };
    return (<>
      <Button
        disabled={disabled}
        type="primary"
        onClick={handleOpen}
      >
        {title ?? '新增壳工程变更'}
      </Button>
      <ModalContent
        open={open}
        onCancel={handleClose}
        title={title}
        {...restProps}
      />
    </>);
};

export default AddMainFrameworkModal;
