@import '@ali/mc-uikit/esm/styles/vars.less';

.dashboardSider {
  height: 100%;
  // padding: var(--mc-padding) var(--mc-padding-lg);
  // border-right: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
}

.title {
  color: var(--mc-color-text);
  font-size: var(--mc-font-size-lg);
  font-weight: var(--mc-font-weight-strong);
}

.spaceLink:hover {
  text-decoration: none!important;
}

.spaceInfoCard {
  padding: var(--mc-padding);
  border: var(--mc-line-width) var(--mc-line-type) var(--mc-color-border);
  border-radius: var(--mc-border-radius);

  .name {
    font-weight: 500;
    color: var(--mc-color-text);
    font-size: var(--mc-font-size);
    word-break: break-word;
    overflow-wrap: break-word;
    margin-right: var(--mc-margin-xs);
  }

  .sub {
    margin-top: var(--mc-margin-sm);
    color: var(--mc-color-text-secondary);
    font-size: var(--mc-font-size-sm);
    .subName {
      font-weight: 500;
      margin-left: var(--mc-margin-xs);
    }
  }

  &:hover {
    background: var(--mc-color-fill-content);
  }
}

[data-color-mode='dark'] {
  .spaceInfoCard:hover {
    background-color: rgba(@dark-base-color-neutral-8, 0.95);
  }
}

.link {
  font-size: var(--mc-font-size-sm);
  font-weight: var(--mc-font-weight-strong);
  color: var(--mc-color-primary);
  // border-bottom: var(--mc-line-width) var(--mc-line-type) var(--mc-color-primary);
}
