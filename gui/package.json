{"name": "gui", "private": true, "type": "module", "author": "Continue Dev, Inc", "license": "Apache-2.0", "scripts": {"dev": "vite", "tsc:check": "tsc -p ./ --noEmit", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:watch": "vitest", "lint": "eslint --ext ts"}, "dependencies": {"@continuedev/config-yaml": "file:../packages/config-yaml", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.0.18", "@reduxjs/toolkit": "^2.3.0", "@tiptap/core": "^2.3.2", "@tiptap/extension-document": "^2.3.2", "@tiptap/extension-dropcursor": "^2.1.16", "@tiptap/extension-history": "^2.3.2", "@tiptap/extension-image": "^2.1.16", "@tiptap/extension-mention": "^2.1.13", "@tiptap/extension-paragraph": "^2.3.2", "@tiptap/extension-placeholder": "^2.1.13", "@tiptap/extension-text": "^2.3.2", "@tiptap/pm": "^2.1.13", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/suggestion": "^2.1.13", "@types/uuid": "^10.0.0", "anser": "^2.3.2", "clsx": "^2.1.1", "core": "file:../core", "dompurify": "^3.0.6", "downshift": "^7.6.0", "escape-carriage": "^1.3.1", "lodash": "^4.17.21", "lowlight": "^3.3.0", "minisearch": "^7.0.2", "mustache": "^4.2.0", "posthog-js": "^1.130.1", "prismjs": "^1.29.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-hook-form": "^7.47.0", "react-intersection-observer": "^9.13.1", "react-markdown": "^9.0.1", "react-redux": "^8.0.5", "react-remark": "^2.1.0", "react-router-dom": "^6.14.2", "react-switch": "^7.0.0", "react-syntax-highlighter": "^15.5.0", "react-tooltip": "^5.18.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-persist-transform-filter": "^0.0.22", "redux-thunk": "^3.1.0", "rehype-highlight": "^7.0.0", "rehype-katex": "^7.0.1", "rehype-wrap-all": "^1.1.0", "remark-math": "^6.0.0", "reselect": "^5.1.1", "seti-file-icons": "^0.0.8", "socket.io-client": "^4.7.2", "styled-components": "^5.3.6", "table": "^6.8.1", "tailwind-merge": "^3.0.2", "tippy.js": "^6.3.7", "unist-util-visit": "^5.0.0", "uuid": "^9.0.1"}, "devDependencies": {"@swc/cli": "^0.3.14", "@swc/core": "^1.7.26", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/dompurify": "^3.2.0", "@types/lodash": "^4.17.6", "@types/mustache": "^4.2.5", "@types/node": "^20.5.6", "@types/node-fetch": "^2.6.4", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/react-router-dom": "^5.3.3", "@types/react-syntax-highlighter": "^15.5.7", "@types/redux-logger": "^3.0.13", "@types/styled-components": "^5.1.26", "@vitejs/plugin-react-swc": "^3.9.0", "@vitest/coverage-v8": "^2.1.3", "@vitest/ui": "^2.1.3", "autoprefixer": "^10.4.13", "eslint": "^8", "eslint-plugin-no-barrel-files": "^1.2.1", "eslint-plugin-react": "^7.37.2", "jsdom": "^25.0.1", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "typescript": "^5.6.3", "typescript-eslint": "^8.16.0", "vite": "^6.3.4", "vitest": "^2.1.3"}, "engine-strict": true, "engines": {"node": ">=20.19.0"}}