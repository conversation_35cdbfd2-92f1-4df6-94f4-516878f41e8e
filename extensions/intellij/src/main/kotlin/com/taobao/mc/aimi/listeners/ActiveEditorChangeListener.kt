package com.taobao.mc.aimi.listeners

import com.github.continuedev.continueintellijextension.services.ContinuePluginService
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.service
import com.intellij.openapi.fileEditor.FileEditor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerEvent
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.MessageTypes
import com.taobao.mc.aimi.util.readText
import kotlin.properties.Delegates

/**
 * 监听活动编辑器变化的监听器
 * 当文件打开、关闭或选择变化时，检测当前激活的编辑器是否发生变化
 * 如果有变化，向 webview 发送通知
 * 如果 webview 还未就绪，会将编辑器引用存储起来，等 webview 就绪后再发送
 */
class ActiveEditorChangeListener(private val project: Project) : FileEditorManagerListener, DumbAware {

    private val logger = LoggerManager.getLogger(javaClass)
    private var lastActiveFile: VirtualFile? = null

    private val continuePluginService = project.service<ContinuePluginService>()

    private var isWebviewReady by Delegates.observable(false) { _, _, newValue ->
        if (!newValue) return@observable
        checkAndNotifyEditorChange(FileEditorManager.getInstance(project))
    }

    init {
        // 注册 webview 就绪回调
        continuePluginService.onProtocolClientInitialized {
            isWebviewReady = true
        }

        // 检查当前是否已经就绪
        if (continuePluginService.ideProtocolClient != null) {
            isWebviewReady = true
        }
    }

    override fun fileOpened(source: FileEditorManager, file: VirtualFile) {
        checkAndNotifyEditorChange(source)
    }

    override fun fileClosed(source: FileEditorManager, file: VirtualFile) {
        checkAndNotifyEditorChange(source)
    }

    override fun selectionChanged(event: FileEditorManagerEvent) {
        checkAndNotifyEditorChange(event.manager)
    }

    private fun checkAndNotifyEditorChange(fileEditorManager: FileEditorManager) {
        ApplicationManager.getApplication().invokeLater {
            val currentEditor = fileEditorManager.selectedEditor
            val currentActiveFile = currentEditor?.file

            // 检查激活的编辑器是否发生变化
            if (currentActiveFile != lastActiveFile) {
                lastActiveFile = currentActiveFile

                // 向 webview 发送通知
                notifyWebviewOfEditorChange(currentEditor)
            }
        }
    }

    private fun notifyWebviewOfEditorChange(editor: FileEditor?) {
        try {
            if (isWebviewReady) {
                // webview 已就绪，直接发送
                sendNotificationToWebview(editor)
            }
        } catch (e: Exception) {
            logger.warn("Failed to handle active editor change", e)
        }
    }

    private fun sendNotificationToWebview(editor: FileEditor?) {
        try {
            val activeFile = editor?.file
            val data = mapOf(
                "filepath" to (activeFile?.url ?: ""),
                "filename" to (activeFile?.name ?: ""),
                "timestamp" to System.currentTimeMillis(),
                "contents" to (editor?.readText() ?: ""),
            )

            val continuePluginService = project.service<ContinuePluginService>()
            continuePluginService.sendToWebview(
                MessageTypes.ToWebview.ActiveEditorChanged,
                data
            )
            logger.info("Notified webview of active editor change: ${data["filename"]}")
        } catch (e: Exception) {
            logger.warn("Failed to send notification to webview", e)
        }
    }
}