package com.taobao.mc.aimi.types

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable
data class ChangeWindowParams(
    @Serializable(with = WindowTypeSerializer::class) val type: WindowType, val url: String?
)

// chat history integration
@Serializable
sealed class WindowType(val type: String) {
    data object Chat : WindowType("chat")
    data object History : WindowType("history")
    data object Integration : WindowType("integration")
    data object API : WindowType("api")
    data object Continue : WindowType("continue")
}

object WindowTypeSerializer : KSerializer<WindowType> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("WindowType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: WindowType) {
        encoder.encodeString(value.type.lowercase())
    }

    override fun deserialize(decoder: Decoder): WindowType {
        val typeString = decoder.decodeString().lowercase()
        return when (typeString) {
            "chat" -> WindowType.Chat
            "history" -> WindowType.History
            "integration" -> WindowType.Integration
            "api" -> WindowType.API
            "continue" -> WindowType.Continue
            else -> throw IllegalArgumentException("Unknown WindowType: $typeString")
        }
    }
}

@Serializable
data class ShowProgressParams(
    val id: String,
    val title: String,
    val progress: Double = 0.0,
    val status: String = "start",
    @Serializable(with = ProgressTypeSerializer::class)
    val type: ProgressType = ProgressType.Apply,
)

@Serializable
sealed class ProgressType(val type: String) {
    data object Apply : ProgressType("apply")
    data object Chat : ProgressType("chat")
}

object ProgressTypeSerializer : KSerializer<ProgressType> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor("ProgressType", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: ProgressType) {
        encoder.encodeString(value.type.lowercase())
    }

    override fun deserialize(decoder: Decoder): ProgressType {
        val typeString = decoder.decodeString().lowercase()
        return when (typeString) {
            "apply" -> ProgressType.Apply
            "chat" -> ProgressType.Chat
            else -> throw IllegalArgumentException("Unknown ProgressType: $typeString")
        }
    }
}