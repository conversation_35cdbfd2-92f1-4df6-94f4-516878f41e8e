package com.taobao.mc.aimi.util

import com.intellij.ui.content.Content
import com.taobao.mc.aimi.types.WindowType
import kotlin.reflect.KProperty

// 创建一个属性委托类
class ContentTypeProperty {
    private val contentTypeMap = mutableMapOf<Content, WindowType?>()

    operator fun getValue(content: Content, property: KProperty<*>): WindowType? {
        return contentTypeMap[content]
    }

    operator fun setValue(content: Content, property: KProperty<*>, value: WindowType?) {
        contentTypeMap[content] = value
    }
}

// 定义扩展属性
var Content.type: WindowType? by ContentTypeProperty()