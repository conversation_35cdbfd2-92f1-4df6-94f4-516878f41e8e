package com.taobao.mc.aimi.startup

import com.github.continuedev.continueintellijextension.actions.toggleAIMI
import com.github.continuedev.continueintellijextension.activities.ContinuePluginDisposable
import com.github.continuedev.continueintellijextension.services.ContinuePluginService
import com.intellij.ide.ui.LafManagerListener
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.EDT
import com.intellij.openapi.components.service
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import com.taobao.mc.aimi.listeners.ActiveEditorChangeListener
import com.taobao.mc.aimi.listeners.ThemeChangeListener
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.settings.AIMISettingsListener
import com.taobao.mc.aimi.settings.AIMIState
import com.taobao.mc.aimi.types.MessageTypes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicInteger

/**
 * AIMI 启动活动
 */
class AIMIStartupActivity : ProjectActivity {

    private val logger = LoggerManager.getLogger(javaClass)

    override suspend fun execute(project: Project) {
        // 注册主题变更监听器
        val disposable = ContinuePluginDisposable.getInstance(project)
        val connection = ApplicationManager.getApplication().messageBus.connect(disposable)
        connection.subscribe(LafManagerListener.TOPIC, ThemeChangeListener())
        
        // 注册活动编辑器变化监听器
        val activeEditorChangeListener = ActiveEditorChangeListener(project)
        connection.subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, activeEditorChangeListener)
        
        // 工程打开后, 进行一次 indexing
        val continuePluginService = project.service<ContinuePluginService>()
        continuePluginService.coreMessengerManager.afterChange(continuePluginService) { manager ->
            manager?.coreMessenger?.afterChange(continuePluginService) { messenger ->
                messenger?.request(
                    messageType = MessageTypes.ToCore.IndexForceReIndex.type,
                    data = mapOf("shouldClearIndexes" to false)
                )
            }
        }

        continuePluginService.listeners.add {
            continuePluginService.coroutineScope.launch(Dispatchers.EDT) {
                toggleAIMI(project, false)
            }
        }

        // 监听 AIMI 设置, 将 部分设置同步到 core
        connection.subscribe(AIMISettingsListener.TOPIC, object : AIMISettingsListener {
            private val lastModified = AtomicInteger(0)
            override fun settingsUpdated(settings: AIMIState) {
                if (settings.modify.get() == lastModified.get()) {
                    return
                }
                lastModified.set(settings.modify.get())

                // 更新所有窗口的URL（如果环境发生变化）
                continuePluginService.updateAllWindowsUrl()
                
                continuePluginService.coreMessengerManager.get()?.coreMessenger?.get()?.apply {
                    request(
                        messageType = MessageTypes.ToCore.ConfigUpdateSharedConfig.type,
                        data = mapOf(
                            "useAutocompleteMultilineCompletions" to settings.multilineCompletions,
                            "enableCrossFileComplete" to settings.enableCrossFileComplete,
                            "debounceDelay" to settings.debounceDelay,
                            "environment" to settings.environment
                        )
                    ) {
                        logger.info("updatedSharedConfig: $it")
                    }
                }
            }
        })
    }
}