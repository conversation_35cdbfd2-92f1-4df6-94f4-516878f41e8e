package com.github.continuedev.continueintellijextension.autocomplete

import com.github.continuedev.continueintellijextension.activities.ContinuePluginDisposable
import com.github.continuedev.continueintellijextension.services.ContinuePluginService
import com.intellij.openapi.Disposable
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.DataContext
import com.intellij.openapi.actionSystem.DefaultActionGroup
import com.intellij.openapi.components.service
import com.intellij.openapi.options.ShowSettingsUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.ui.popup.JBPopupFactory
import com.intellij.openapi.util.Disposer
import com.intellij.openapi.wm.StatusBar
import com.intellij.openapi.wm.StatusBarWidget
import com.intellij.openapi.wm.StatusBarWidgetFactory
import com.intellij.openapi.wm.WindowManager
import com.intellij.openapi.wm.impl.status.EditorBasedWidget
import com.intellij.ui.AnimatedIcon
import com.intellij.util.Consumer
import com.intellij.vcsUtil.showAbove
import com.taobao.mc.aimi.autocomplete.ToggleTabAutocompleteAction
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.settings.AIMISettingService
import com.taobao.mc.aimi.types.MessageTypes
import com.taobao.mc.aimi.util.AIMIIcons
import java.awt.Component
import java.awt.event.MouseEvent
import javax.swing.Icon
import javax.swing.JLabel

class AutocompleteSpinnerWidget(project: Project) : EditorBasedWidget(project), StatusBarWidget.IconPresentation, Disposable {
    private val logger = LoggerManager.getLogger(AutocompleteSpinnerWidget::class.java)

    private val iconLabel = JLabel()
    private var isLoading = false

    private val animatedIcon = AnimatedIcon.Default()

    init {
        Disposer.register(ContinuePluginDisposable.getInstance(project), this)
        updateIcon()
    }

    fun show(clickedComponent: Component) {
        logger.info("Showing autocomplete spinner widget")
        val settings = service<AIMISettingService>()
        val enabled = settings.state.enableTabAutocomplete

        val actionGroup = DefaultActionGroup().apply {
            add(OpenSettingsAction())
            add(ToggleTabAutocompleteAction("${if (enabled) "关闭" else "开启"}自动补全 ⌘\\"))
            add(ReIndexAction())
        }

        val popup = JBPopupFactory.getInstance()
            .createActionGroupPopup(
                "AIMI",
                actionGroup,
                DataContext.EMPTY_CONTEXT,
                JBPopupFactory.ActionSelectionAid.SPEEDSEARCH,
                true,
                null
            )

        popup.showAbove(clickedComponent)
    }

    override fun dispose() {}

    override fun ID(): String {
        return ID
    }

    override fun getTooltipText(): String {
        val enabled = service<AIMISettingService>().state.enableTabAutocomplete
        return if (enabled) "AIMI autocomplete enabled" else "AIMI autocomplete disabled"
    }

    override fun getClickConsumer(): Consumer<MouseEvent> {
        return Consumer { event ->
            show(event.component)
        }
    }

    override fun getIcon(): Icon = if (isLoading) animatedIcon else AIMIIcons.AIMI

    fun setLoading(loading: Boolean) {
        isLoading = loading
        updateIcon()
    }

    private fun updateIcon() {
        iconLabel.icon = getIcon()


        // Update the widget
        val statusBar = WindowManager.getInstance().getStatusBar(project)
        statusBar?.updateWidget(ID())
    }

    override fun install(statusBar: StatusBar) {
        updateIcon()
    }

    override fun getPresentation(): StatusBarWidget.WidgetPresentation {
        return this
    }

    companion object {
        const val ID = "AutocompleteSpinnerWidget"
    }
}

class AutocompleteSpinnerWidgetFactory : StatusBarWidgetFactory {
    fun create(project: Project): AutocompleteSpinnerWidget {
        return AutocompleteSpinnerWidget(project)
    }

    override fun getId(): String {
        return AutocompleteSpinnerWidget.ID
    }

    override fun getDisplayName(): String {
        return "AIMI Autocomplete"
    }

    override fun isAvailable(p0: Project): Boolean {
        return true
    }

    override fun createWidget(project: Project): StatusBarWidget {
        return create(project)
    }

    override fun disposeWidget(p0: StatusBarWidget) {
        Disposer.dispose(p0)
    }

    override fun canBeEnabledOn(p0: StatusBar): Boolean = true
}

private class OpenSettingsAction : AnAction("设置") {
    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project
        // 直接打开 AIMI Settings配置页面
        ShowSettingsUtil.getInstance().showSettingsDialog(project, "AIMI")
    }
}

private class ReIndexAction : AnAction("重建缓存") {
    override fun actionPerformed(e: AnActionEvent) {
        e.project?.apply {
            with(service<ContinuePluginService>()) {
                coreMessenger?.request(
                    messageType = MessageTypes.ToCore.IndexForceReIndex.type,
                    data = mapOf("shouldClearIndexes" to true)
                )
            }
        }
    }
}