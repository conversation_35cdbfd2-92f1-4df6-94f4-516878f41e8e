package com.github.continuedev.continueintellijextension.actions

import com.github.continuedev.continueintellijextension.services.ContinuePluginService
import com.intellij.openapi.components.serviceOrNull
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager
import com.taobao.mc.aimi.types.Prompts
import com.taobao.mc.aimi.types.WindowType

fun getAIMIToolWindow(project: Project?) = if (project != null) ToolWindowManager.getInstance(project).getToolWindow("AIMI") else null

fun toggleAIMI(project: Project?, canHide: Boolean): Boolean {
    if (project != null) {
        val toolWindowManager = ToolWindowManager.getInstance(project)
        val toolWindow = toolWindowManager.getToolWindow("AIMI")

        if (toolWindow != null) {
            if (!toolWindow.isVisible) {
                toolWindow.activate(null)
            } else if (canHide) {
                toolWindow.hide()
                return true
            }
        }
    }
    return false
}

fun getContinuePluginService(project: Project?, canHide: Boolean = false): ContinuePluginService? {
    if (toggleAIMI(project, canHide)) return null

    return getPluginService(project)
}

fun focusContinueInput(project: Project?, newSession: Boolean = false, canHide: Boolean = false) {
    getContinuePluginService(project, canHide)?.apply {
        activeContent?.components?.firstOrNull()?.requestFocus()
        changeWindow(WindowType.Chat, null, false)
        sendToWebview(if (newSession) "focusInputWithClear" else "focusInputWithoutClear", null)
        ideProtocolClient?.sendHighlightedCode(true, Prompts.CodeOptimization.prompt)
    }
}

fun getPluginService(project: Project?): ContinuePluginService? {
    return project?.serviceOrNull<ContinuePluginService>()
}