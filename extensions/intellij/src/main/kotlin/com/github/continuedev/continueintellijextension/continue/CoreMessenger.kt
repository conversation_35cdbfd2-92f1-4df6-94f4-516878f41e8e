package com.github.continuedev.continueintellijextension.`continue`

import com.github.continuedev.continueintellijextension.services.ContinuePluginService
import com.github.continuedev.continueintellijextension.services.TelemetryService
import com.github.continuedev.continueintellijextension.utils.uuid
import com.google.gson.Gson
import com.intellij.execution.configurations.GeneralCommandLine
import com.intellij.execution.util.ExecUtil
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.MessageTypes
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.*
import java.net.Socket
import java.nio.charset.StandardCharsets
import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.attribute.PosixFilePermission

class CoreMessenger(
    private val project: Project,
    continueCorePath: String,
    private val ideProtocolClient: IdeProtocolClient,
    val coroutineScope: CoroutineScope
) {
    private var writer: Writer? = null
    private var reader: BufferedReader? = null
    private var process: Process? = null
    private val gson = Gson()
    private val responseListeners = mutableMapOf<String, (Any?) -> Unit>()
    private val useTcp: Boolean = System.getenv("USE_TCP")?.toBoolean() ?: false
    private val logger = LoggerManager.getLogger(CoreMessenger::class.java)

    private fun write(message: String) {
        coroutineScope.launch(Dispatchers.IO) {
            try {
                writer?.write(message + "\r\n")
                writer?.flush()
            } catch (e: Exception) {
                logger.warnWithDebug("Error writing to AIMI core: $message", e)
            }
        }
    }

    fun request(messageType: String, data: Any?, messageId: String? = uuid(), onResponse: (Any?) -> Unit = {}) {
        val id = messageId ?: uuid()
        val message =
            gson.toJson(mapOf("messageId" to id, "messageType" to messageType, "data" to data))
        responseListeners[id] = onResponse
        write(message)
    }

    private fun handleMessage(json: String) {
        val responseMap = runCatching {
            gson.fromJson(json, Map::class.java)
        }.onFailure {
            logger.warnWithDebug("Error parsing message: ", it)
        }.getOrNull() ?: run {
            logger.warn("Error parsing message: $json")
            return
        }
        val messageId = responseMap["messageId"].toString()
        val messageType = MessageTypes.from(responseMap["messageType"].toString()) ?: run {
            logger.warn("Unknown message: $json")
            return
        }
        val data = responseMap["data"]

        // IDE listeners
        if (MessageTypes.ideMessageTypes.contains(messageType)) {
            ideProtocolClient.handleMessage(json) { data ->
                val message =
                    gson.toJson(
                        mapOf("messageId" to messageId, "messageType" to messageType, "data" to data)
                    )
                write(message)
            }
        }

        // Forward to webview
        if (MessageTypes.webviewMessageTypes.contains(messageType)) {
            val continuePluginService = project.service<ContinuePluginService>()
            continuePluginService.sendToWebview(messageType, responseMap["data"])
        }

        // Responses for messageId
        responseListeners[messageId]?.let { listener ->
            listener(data)
            val done = (data as Map<String, Boolean>)["done"]

            if (done == true) {
                responseListeners.remove(messageId)
            }
        }
    }

    private fun setPermissions(destination: String) {
        val osName = System.getProperty("os.name").lowercase()
        if (osName.contains("mac") || osName.contains("darwin")) {
            val commandLine = GeneralCommandLine("xattr", "-dr", "com.apple.quarantine", destination)
            ExecUtil.execAndGetOutput(commandLine)
            setFilePermissions(destination, "rwxr-xr-x")
        } else if (osName.contains("nix") || osName.contains("nux")) {
            setFilePermissions(destination, "rwxr-xr-x")
        }
    }

    private fun setFilePermissions(path: String, posixPermissions: String) {
        val perms = HashSet<PosixFilePermission>()
        if (posixPermissions.contains("r")) perms.add(PosixFilePermission.OWNER_READ)
        if (posixPermissions.contains("w")) perms.add(PosixFilePermission.OWNER_WRITE)
        if (posixPermissions.contains("x")) perms.add(PosixFilePermission.OWNER_EXECUTE)
        Files.setPosixFilePermissions(Paths.get(path), perms)
    }

    private val exitCallbacks: MutableList<() -> Unit> = mutableListOf()

    fun onDidExit(callback: () -> Unit) {
        exitCallbacks.add(callback)
    }

    init {
        if (useTcp) {
            logger.info("Starting AIMI core with TCP")
            try {
                val socket = Socket("127.0.0.1", 3000)
                val writer = PrintWriter(socket.getOutputStream(), true)
                this.writer = writer
                val reader = BufferedReader(InputStreamReader(socket.getInputStream()))
                this.reader = reader

                Thread {
                    try {
                        while (true) {
                            val line = reader.readLine()
                            if (line != null && line.isNotEmpty()) {
                                try {
                                    handleMessage(line)
                                } catch (e: Exception) {
                                    logger.warn("Error handling message: $line", e)
                                }
                            } else {
                                Thread.sleep(100)
                            }
                        }
                    } catch (e: IOException) {
                        logger.warn("Exception occurred", e)
                    } finally {
                        try {
                            reader.close()
                            writer.close()
                        } catch (e: IOException) {
                            logger.warn("Exception occurred", e)
                        }
                    }
                }
                    .start()
            } catch (e: Exception) {
                logger.warn("TCP Connection Error: Unable to connect to 127.0.0.1:3000. Reason: ${e.message}", e)
            }
        } else {
            logger.info("Starting AIMI core with path: $continueCorePath")
            // Set proper permissions synchronously
            coroutineScope.launch(Dispatchers.IO) {
                setPermissions(continueCorePath)
            }

            // Start the subprocess
            val commandLine = GeneralCommandLine(continueCorePath)
                .withWorkDirectory(UriUtils.uriToFile(continueCorePath).parentFile)
            process = commandLine.createProcess()

            val outputStream = process!!.outputStream
            val inputStream = process!!.inputStream

            writer = OutputStreamWriter(outputStream, StandardCharsets.UTF_8)
            reader = BufferedReader(InputStreamReader(inputStream, StandardCharsets.UTF_8))

            process!!.onExit().thenRun {
                exitCallbacks.forEach { it() }
                var err = process?.errorStream?.bufferedReader()?.readText()?.trim()
                if (err != null) {
                    // There are often "⚡️Done in Xms" messages, and we want everything after the last one
                    val delimiter = "⚡ Done in"
                    val doneIndex = err.lastIndexOf(delimiter)
                    if (doneIndex != -1) {
                        err = err.substring(doneIndex + delimiter.length)
                    }
                }

                logger.info("Core process exited with output: $err")

                // Log the cause of the failure
                val telemetryService = service<TelemetryService>()
                telemetryService.capture("jetbrains_core_exit", mapOf("error" to err))

                // Clean up all resources
                writer?.close()
                reader?.close()
                process?.destroy()
            }

            coroutineScope.launch(Dispatchers.IO) {
                try {
                    while (true) {
                        val line = reader?.readLine()
                        if (line != null && line.isNotEmpty()) {
                            try {
                                handleMessage(line)
                            } catch (e: Exception) {
                                logger.warn("Error handling message: $line", e)
                            }
                        } else {
                            delay(100)
                        }
                    }
                } catch (e: IOException) {
                    logger.warn("Exception occurred", e)
                } finally {
                    try {
                        reader?.close()
                        writer?.close()
                        outputStream.close()
                        inputStream.close()
                        process?.destroy()
                    } catch (e: IOException) {
                        logger.warn("Exception occurred", e)
                    }
                }
            }
        }
    }

    fun killSubProcess() {
        try {
            // 清理回调
            exitCallbacks.clear()

            // 清理响应监听器
            responseListeners.clear()

            // 关闭 IO 流
            writer?.close()
            reader?.close()

            // 销毁进程
            process?.let { proc ->
                if (proc.isAlive) {
                    proc.destroy()
                    // 如果进程仍然存活，强制终止
                    if (!proc.waitFor(5, java.util.concurrent.TimeUnit.SECONDS)) {
                        proc.destroyForcibly()
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("Error killing subprocess", e)
        } finally {
            writer = null
            reader = null
            process = null
        }
    }
}