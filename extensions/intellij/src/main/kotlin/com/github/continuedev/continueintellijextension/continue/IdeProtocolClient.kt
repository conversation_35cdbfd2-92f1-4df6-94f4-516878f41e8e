package com.github.continuedev.continueintellijextension.`continue`

import com.github.continuedev.continueintellijextension.*
import com.github.continuedev.continueintellijextension.activities.ContinuePluginDisposable
import com.github.continuedev.continueintellijextension.activities.showTutorial
import com.github.continuedev.continueintellijextension.auth.ContinueAuthService
import com.github.continuedev.continueintellijextension.auth.ControlPlaneSessionInfo
import com.github.continuedev.continueintellijextension.editor.DiffStreamService
import com.github.continuedev.continueintellijextension.editor.EditorUtils
import com.github.continuedev.continueintellijextension.protocol.*
import com.github.continuedev.continueintellijextension.services.ContinuePluginService
import com.github.continuedev.continueintellijextension.utils.getMachineUniqueID
import com.github.continuedev.continueintellijextension.utils.uuid
// 添加策略模式相关的导入
import com.github.continuedev.continueintellijextension.`continue`.handlers.MessageHandlerFactory
import com.github.continuedev.continueintellijextension.`continue`.handlers.MessageHandlerContext
import com.google.gson.Gson
import com.intellij.ide.DataManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.EDT
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.components.service
import com.intellij.openapi.editor.SelectionModel
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFileManager
import com.intellij.openapi.wm.ToolWindowManager
import com.intellij.util.application
import com.taobao.mc.aimi.execution.AIMI_CONFIGURATION_NAME
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.services.search.SymbolSearchService
import com.taobao.mc.aimi.settings.AIMISettingService
import com.taobao.mc.aimi.settings.IndexUpdateStatus
import com.taobao.mc.aimi.types.ChangeWindowParams
import com.taobao.mc.aimi.types.MessageTypes
import com.taobao.mc.aimi.types.MessageTypes.ToIDE
import com.taobao.mc.aimi.types.ShowProgressParams
import com.taobao.mc.aimi.util.kJson
import com.taobao.mc.aimi.util.mSelectedTextEditor
import kotlinx.coroutines.*
import java.awt.Toolkit
import java.awt.datatransfer.StringSelection


class IdeProtocolClient(
    private val continuePluginService: ContinuePluginService,
    private val coroutineScope: CoroutineScope,
    private val project: Project
) : DumbAware {
    val ide: IDE = IntelliJIDE(project, continuePluginService)
    private val diffStreamService = project.service<DiffStreamService>()
    private val logger = LoggerManager.getLogger(javaClass)

    // 添加消息处理器注册表
    private val messageHandlerRegistry = MessageHandlerFactory.createHandlerRegistry()
    
    // 创建消息处理上下文
    private val handlerContext = MessageHandlerContext(
        ide = ide,
        project = project,
        continuePluginService = continuePluginService,
        diffStreamService = diffStreamService,
        coroutineScope = coroutineScope,
        logger = logger
    )

    /**
     * Create a dispatcher with limited parallelism to prevent UI freezing.
     * Note that there are 64 total threads available to the IDE.
     *
     * See this thread for details: https://github.com/continuedev/continue/issues/4098#issuecomment-2854865310
     */
    @OptIn(ExperimentalCoroutinesApi::class)
    private val limitedDispatcher = Dispatchers.IO.limitedParallelism(4)

    init {
        // Setup config.json / config.ts save listeners
        VirtualFileManager.getInstance().addAsyncFileListener(
            AsyncFileSaveListener(continuePluginService), ContinuePluginDisposable.getInstance(project)
        )
    }

    fun handleMessage(msg: String, respond: (Any?) -> Unit) {
        coroutineScope.launch(limitedDispatcher) {
            val gson = Gson()
            val message = gson.fromJson(msg, Message::class.java)
            val messageType = MessageTypes.from(message.messageType) ?: run {
                logger.warn("Unknown message type: ${message.messageType}")
                return@run
            }
            val dataElement = message.data

            try {
                // 使用策略模式处理消息
                val handler = messageHandlerRegistry.getHandler(messageType)
                if (handler != null) {
                    handler.handle(dataElement, handlerContext, respond)
                } else {
                    logger.warn("No handler found for message type: $messageType")
                    respond(null)
                }
            } catch (error: Exception) {
                logger.error("Error handling message of type ${message.messageType}: $error", error)
                ide.showToast(ToastType.ERROR, " Error handling message of type ${message.messageType}: $error")
            }
        }
    }

    fun sendHighlightedCode(edit: Boolean = false, prompt: String? = null) {
        val editor = EditorUtils.getEditor(project)
        val rif = editor?.getHighlightedRIF() ?: return

        continuePluginService.sendToWebview(
            "highlightedCode",
            HighlightedCodePayload(
                rangeInFileWithContents = rif,
                shouldRun = edit,
                prompt = prompt,
            )
        )
    }


    fun sendAcceptRejectDiff(accepted: Boolean, stepIndex: Int) {
        continuePluginService.sendToWebview("acceptRejectDiff", AcceptRejectDiff(accepted, stepIndex), uuid())
    }


    fun deleteAtIndex(index: Int) {
        continuePluginService.sendToWebview("deleteAtIndex", DeleteAtIndex(index), uuid())
    }
}