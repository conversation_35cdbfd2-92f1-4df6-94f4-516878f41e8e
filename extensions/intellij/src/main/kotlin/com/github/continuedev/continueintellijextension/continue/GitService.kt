package com.github.continuedev.continueintellijextension.`continue`

import com.github.continuedev.continueintellijextension.services.ContinuePluginService
import com.github.continuedev.continueintellijextension.utils.toUriOrNull
import com.intellij.execution.configurations.GeneralCommandLine
import com.intellij.execution.util.ExecUtil
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.guessProjectDir
import com.taobao.mc.aimi.logger.LoggerManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class GitService(
    private val project: Project,
    private val continuePluginService: ContinuePluginService
) {
    private val logger = LoggerManager.getLogger(javaClass)

    /**
     * Returns the git diff for all workspace directories
     */
    suspend fun getDiff(includeUnstaged: Boolean): List<String> {
        val workspaceDirs = workspaceDirectories()
        val diffs = mutableListOf<String>()

        for (workspaceDir in workspaceDirs) {
            val commandList = if (includeUnstaged) {
                listOf("git", "diff")
            } else {
                listOf("git", "diff", "--cached")
            }

            val generalCommandLine = GeneralCommandLine(commandList)
                .withWorkDirectory(UriUtils.uriToFileSafe(workspaceDir))
            runCatching {
                val output = withContext(Dispatchers.IO) {
                    ExecUtil.execAndGetOutput(generalCommandLine)
                }
                diffs.add(output.stdout)
            }.onFailure {
                logger.warn("Error getting diff for workspace dir: ${workspaceDir}, error: ${it.message}")
            }
        }

        return diffs
    }

    private fun workspaceDirectories(): Array<String> {
        val dirs = this.continuePluginService.workspacePaths

        if (dirs?.isNotEmpty() == true) {
            return dirs
        }

        return listOfNotNull(project.guessProjectDir()?.toUriOrNull()).toTypedArray()
    }

}