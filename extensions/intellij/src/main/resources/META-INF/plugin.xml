<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <id>com.taobao.mc.aimi</id>
    <name>AIMI</name>
    <vendor url="https://alibaba.com">淘宝</vendor>

    <depends>com.intellij.modules.platform</depends>
    <!--    <depends>org.jetbrains.plugins.terminal</depends>-->
    <!--    <depends>org.jetbrains.kotlin</depends>-->
    <!--    <depends>com.intellij.java</depends>-->

    <!-- See here for why this is optional:  https://github.com/continuedev/continue/issues/2775#issuecomment-2535620877-->
    <depends optional="true" config-file="continueintellijextension-withJSON.xml">
        com.intellij.modules.json
    </depends>

    <!-- com.intellij.openapi.module.ModuleManager.Companion is only available since this build -->
    <idea-version since-build="223.7571.182"/>

    <extensions defaultExtensionNs="com.intellij">
        <postStartupActivity implementation="com.taobao.mc.aimi.startup.ClipboardMonitorStartupActivity"/>
        <postStartupActivity implementation="com.taobao.mc.aimi.startup.AIMIStartupActivity"/>
    </extensions>

    <extensions defaultExtensionNs="com.intellij">
        <editorFactoryListener
                implementation="com.github.continuedev.continueintellijextension.autocomplete.AutocompleteEditorListener"/>
        <toolWindow id="AIMI" anchor="right" icon="com.taobao.mc.aimi.util.AIMIIcons.AIMI"
                    factoryClass="com.github.continuedev.continueintellijextension.toolWindow.ContinuePluginToolWindowFactory"/>
        <projectService id="ContinuePluginService"
                        serviceImplementation="com.github.continuedev.continueintellijextension.services.ContinuePluginService"/>
        <projectService
                id="DiffStreamService"
                serviceImplementation="com.github.continuedev.continueintellijextension.editor.DiffStreamService"/>
        <projectService
                id="AutocompleteLookupListener"
                serviceImplementation="com.github.continuedev.continueintellijextension.autocomplete.AutocompleteLookupListener"/>
        <statusBarWidgetFactory
                implementation="com.github.continuedev.continueintellijextension.autocomplete.AutocompleteSpinnerWidgetFactory"
                id="AutocompleteSpinnerWidget"/>
        <notificationGroup id="AIMI"
                           displayType="BALLOON"/>
        <actionPromoter order="last"
                        implementation="com.github.continuedev.continueintellijextension.actions.ContinueActionPromote"/>
    </extensions>

    <resource-bundle>messages.MyBundle</resource-bundle>

    <extensions defaultExtensionNs="com.intellij">
        <postStartupActivity
                implementation="com.github.continuedev.continueintellijextension.activities.ContinuePluginStartupActivity"/>
        <applicationConfigurable
                parentId="tools"
                instance="com.taobao.mc.aimi.settings.AIMISettingsConfigurable"
                id="com.taobao.mc.aimi.settings.AIMISettingsConfigurable"
                displayName="AIMI"/>
        <applicationService
                serviceImplementation="com.taobao.mc.aimi.settings.AIMISettingService"/>
    </extensions>

    <actions>
        <!--<action class="com.github.continuedev.continueintellijextension.editor.InlineEditAction"
                id="aimi.inlineEdit"
                description="Inline Edit"
                text="Inline Edit">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="ctrl I"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="meta I"/>
            <override-text place="GoToAction" text="AIMI: Edit Code"/>
        </action>-->

        <action id="aimi.acceptDiff"
                class="com.github.continuedev.continueintellijextension.actions.AcceptDiffAction"
                text="Accept Diff" description="Accept Diff">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="shift ctrl ENTER"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="shift meta ENTER"/>
            <override-text place="GoToAction" text="AIMI: Accept Diff"/>
        </action>

        <action id="aimi.rejectDiff"
                class="com.github.continuedev.continueintellijextension.actions.RejectDiffAction"
                text="Reject Diff" description="Reject Diff">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="shift ctrl BACK_SPACE"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="shift meta BACK_SPACE"/>
            <override-text place="GoToAction" text="AIMI: Reject Diff"/>
        </action>

        <action id="aimi.acceptVerticalDiffBlock"
                class="com.github.continuedev.continueintellijextension.actions.AcceptDiffAction"
                text="Accept Diff" description="Accept Vertical Diff Block">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="alt shift Y"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="alt shift Y"/>
            <override-text place="GoToAction" text="AIMI: Accept Vertical Diff Block"/>
        </action>

        <action id="aimi.rejectVerticalDiffBlock"
                class="com.github.continuedev.continueintellijextension.actions.RejectDiffAction"
                text="Reject Diff" description="Reject Vertical Diff Block">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="alt shift N"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="alt shift N"/>
            <override-text place="GoToAction" text="AIMI: Reject Vertical Diff Block"/>
        </action>

        <action id="aimi.focusContinueInputWithoutClear"
                class="com.github.continuedev.continueintellijextension.actions.FocusContinueInputWithoutClearAction"
                text="添加到对话"
                description="Focus AIMI Input With Edit">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="ctrl shift J"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="meta shift J"/>
            <override-text place="GoToAction" text="AIMI: Add Highlighted Code to Context"/>
        </action>

        <action id="aimi.newContinueSession"
                icon="AllIcons.General.Add"
                class="com.github.continuedev.continueintellijextension.actions.NewContinueSessionAction"
                text="新会话">

            <override-text place="GoToAction" text="New Session"/>
        </action>

        <action id="aimi.viewHistory"
                icon="AllIcons.Vcs.History"
                class="com.github.continuedev.continueintellijextension.actions.ViewHistoryAction"
                text="View History"
                description="View History">
            <override-text place="GoToAction" text="View History"/>
        </action>

        <action id="aimi.openConfigPage"
                class="com.github.continuedev.continueintellijextension.actions.OpenConfigAction"
                icon="AllIcons.General.GearPlain"
                text="AIMI Config"
                description="AIMI Config">
            <override-text place="GoToAction" text="AIMI Config"/>
        </action>

        <action id="aimi.openLogs"
                class="com.github.continuedev.continueintellijextension.actions.OpenLogsAction"
                icon="AllIcons.General.ShowInfos"
                text="Open Logs"
                description="Open AIMI Logs">
            <override-text place="GoToAction" text="Open AIMI Logs"/>
        </action>

        <action id="aimi.focusContinueInput"
                class="com.github.continuedev.continueintellijextension.actions.FocusContinueInputAction"
                text="添加到对话"
                description="Focus AIMI Input">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="ctrl J"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="meta J"/>
            <override-text place="GoToAction" text="AIMI: Add Highlighted Code to Context and Clear Chat"/>
        </action>

        <action id="com.github.continuedev.continueintellijextension.autocomplete.AcceptAutocompleteAction"
                class="com.github.continuedev.continueintellijextension.autocomplete.AcceptAutocompleteAction"
                text="Accept Autocomplete Suggestion" description="Accept Autocomplete Suggestion">
            <keyboard-shortcut keymap="$default" first-keystroke="TAB"/>
            <keyboard-shortcut keymap="Mac OS X" first-keystroke="TAB"/>
        </action>

        <action id="com.github.continuedev.continueintellijextension.autocomplete.CancelAutocompleteAction"
                class="com.github.continuedev.continueintellijextension.autocomplete.CancelAutocompleteAction"
                text="Cancel Autocomplete Suggestion" description="Cancel Autocomplete Suggestion">
            <keyboard-shortcut keymap="$default" first-keystroke="ESCAPE"/>
        </action>

        <action id="com.github.continuedev.continueintellijextension.autocomplete.PartialAcceptAutocompleteAction"
                class="com.github.continuedev.continueintellijextension.autocomplete.PartialAcceptAutocompleteAction"
                text="Partial Accept Autocomplete Suggestion"
                description="Partial Accept Autocomplete Suggestion">
            <keyboard-shortcut first-keystroke="control alt RIGHT" keymap="$default"/>
            <keyboard-shortcut first-keystroke="alt meta RIGHT" keymap="Mac OS X"/>
        </action>

        <!-- 新增主动触发代码补全的 Action -->
        <action id="aimi.triggerAutocomplete"
                class="com.taobao.mc.aimi.autocomplete.TriggerAutocompleteAction"
                text="触发代码补全"
        >
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl BACK_SLASH"/>
            <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta BACK_SLASH"/>
            <override-text place="GoToAction"/>
        </action>
        <!--新增开关AIMI界面的Action-->
        <action id="aimi.toggleAIMI"
                icon="com.taobao.mc.aimi.util.AIMIIcons.AIMI"
                class="com.taobao.mc.aimi.actions.ToggleAIMIAction"
                text="打开/关闭 AIMI"
        >
            <keyboard-shortcut keymap="$default" first-keystroke="alt A"/>
            <keyboard-shortcut keymap="Mac OS X" first-keystroke="alt A"/>
            <override-text place="GoToAction"/>
        </action>
        <!--新增开关AIMI设置界面的Action-->
        <action id="aimi.openSettings"
                icon="AllIcons.General.GearPlain"
                class="com.github.continuedev.continueintellijextension.autocomplete.OpenSettingsAction"
                text="AIMI设置"
        >
            <override-text place="GoToAction"/>
        </action>
        <!--新增开关AIMI历史会话界面的Action-->
        <action id="aimi.openHistoryWindow"
                icon="AllIcons.Vcs.History"
                class="com.taobao.mc.aimi.actions.OpenHistoryWindowAction"
                text="历史会话"
        >
            <override-text place="GoToAction"/>
        </action>

        <!--新增开关API窗口的Action-->
        <action id="aimi.openAPIWindow"
                icon="AllIcons.Nodes.Interface"
                class="com.taobao.mc.aimi.actions.OpenAPIWindowAction"
                text="API"
        >
            <override-text place="GoToAction"/>
        </action>

        <!--新增开关Continue窗口的Action-->
        <action id="aimi.openContinueWindow"
                icon="AllIcons.Actions.Execute"
                class="com.taobao.mc.aimi.actions.OpenContinueWindowAction"
                text="Continue"
        >
            <override-text place="GoToAction"/>
        </action>

        <!--新增开发者工具Action-->
        <action id="aimi.openDevTools"
                icon="AllIcons.Toolwindows.ToolWindowDebugger"
                class="com.taobao.mc.aimi.actions.OpenDevToolsAction"
                text="开发者工具"
        >
            <override-text place="GoToAction"/>
        </action>

        <!--新增搜索Action-->
        <action id="aimi.search"
                icon="AllIcons.Actions.Search"
                class="com.taobao.mc.aimi.actions.SearchAction"
                text="搜索"
        >
            <override-text place="GoToAction"/>
        </action>

        <!--新增RipGrep搜索Action-->
        <action id="aimi.ripgrepSearch"
                icon="AllIcons.Actions.Find"
                class="com.taobao.mc.aimi.actions.RipGrepSearchAction"
                text="RipGrep搜索"
        >
            <override-text place="GoToAction"/>
        </action>

        <!--刷新页面Action-->
        <action id="aimi.refreshPage"
                icon="AllIcons.Actions.Refresh"
                class="com.taobao.mc.aimi.actions.RefreshPageAction"
                text="刷新页面"
        >
            <override-text place="GoToAction"/>
        </action>

        <group id="aimi.EditorPopupMenu"
               popup="true"
               icon="com.taobao.mc.aimi.util.AIMIIcons.AIMI"
               text="AIMI">
            <reference ref="aimi.focusContinueInput"/>
            <add-to-group group-id="EditorPopupMenu" anchor="first"/>
        </group>

        <group id="AIMISidebarActionsGroup">
            <reference ref="aimi.newContinueSession"/>
            <reference ref="aimi.refreshPage"/>
            <reference ref="aimi.openHistoryWindow"/>
            <reference ref="aimi.openSettings"/>
        </group>

        <group id="AIMISidebarDebugActionsGroup">
            <reference ref="aimi.newContinueSession"/>
            <reference ref="aimi.refreshPage"/>
            <reference ref="aimi.openHistoryWindow"/>
            <reference ref="aimi.search"/>
            <reference ref="aimi.ripgrepSearch"/>
            <reference ref="aimi.openSettings"/>
            <reference ref="aimi.openAPIWindow"/>
            <reference ref="aimi.openContinueWindow"/>
        </group>
    </actions>
</idea-plugin>