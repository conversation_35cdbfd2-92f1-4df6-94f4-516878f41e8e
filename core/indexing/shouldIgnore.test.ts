// Generated by continue

import { testIde } from "../test/fixtures";
import {
  addToTestDir,
  setUpTestDir,
  tearDownTestDir,
  TEST_DIR,
} from "../test/testDir";

import { shouldIgnore } from "./shouldIgnore";

// Test for the shouldIgnore function
describe("shouldIgnore", () => {
  beforeEach(() => {
    setUpTestDir();
  });

  afterEach(() => {
    tearDownTestDir();
  });

  test("should return true if a file is ignored by .gitignore", async () => {
    addToTestDir([
      ["ignored-file.txt", "content"],
      [".gitignore", "ignored-file.txt"],
    ]);
    const result = await shouldIgnore(TEST_DIR + "/ignored-file.txt", testIde, [
      TEST_DIR,
    ]);
    expect(result).toBe(true);
  });
  test("should return true if a folder is ignored by .aimi_ignore", async () => {
    addToTestDir([
      ["ignored-folder/file.txt", "content"],
      [".aimi_ignore", "ignored-folder/"],
    ]);
    const result = await shouldIgnore(
      TEST_DIR + "/ignored-folder/file.txt",
      testIde,
      [TEST_DIR],
    );
    expect(result).toBe(true);
  });
  test("should return false if a file is not ignored", async () => {
    addToTestDir([["kept-file.txt", "contents"]]);
    const result = await shouldIgnore(TEST_DIR + "/kept-file.txt", testIde, [
      TEST_DIR,
    ]);
    expect(result).toBe(false);
  });
  test("should return true if a file is ignored by .gitignore with spaces in file name", async () => {
    addToTestDir([
      ["ignored file.txt", "content"],
      [".gitignore", "ignored file.txt"],
    ]);
    const result = await shouldIgnore(TEST_DIR + "/ignored file.txt", testIde, [
      TEST_DIR,
    ]);
    expect(result).toBe(true);
  });

  test("should return true if a .aimi_ignore override ignores file", async () => {
    addToTestDir([
      ["override-file.txt", "content"],
      [".gitignore", "override-file.txt"],
      [".aimi_ignore", "!override-file.txt"],
    ]);
    const result = await shouldIgnore(
      TEST_DIR + "/override-file.txt",
      testIde,
      [TEST_DIR],
    );
    expect(result).toBe(false);
  });

  test("should return true for deeply nested file ignored by ignore files at multiple levels", async () => {
    addToTestDir([
      ["level1/level2/level3/ignored-file.txt", "content"],
      ["level1/.gitignore", "level2/"],
      ["level1/level2/.aimi_ignore", "level3/"],
    ]);
    const result = await shouldIgnore(
      TEST_DIR + "/level1/level2/level3/ignored-file.txt",
      testIde,
      [TEST_DIR],
    );
    expect(result).toBe(true);
  });

  test("should return false if a file with spaces is not ignored", async () => {
    addToTestDir([["kept file.txt", "contents"]]);
    const result = await shouldIgnore(TEST_DIR + "/kept file.txt", testIde, [
      TEST_DIR,
    ]);
    expect(result).toBe(false);
  });

  test("should handle multiple levels of directories with some files ignored", async () => {
    addToTestDir([
      ["nested/dir1/.gitignore", "ignored.txt"],
      ["nested/dir1/ignored.txt", "content"],
      ["nested/dir2/kept.txt", "content"],
    ]);
    const ignoredResult = await shouldIgnore(
      TEST_DIR + "/nested/dir1/ignored.txt",
      testIde,
      [TEST_DIR],
    );
    expect(ignoredResult).toBe(true);

    const keptResult = await shouldIgnore(
      TEST_DIR + "/nested/dir2/kept.txt",
      testIde,
      [TEST_DIR],
    );
    expect(keptResult).toBe(false);
  });

  test("should respect default file and folder ignores top level", async () => {
    addToTestDir([
      [".env", "contents"],
      ["go.sum", "contents"],
      ".idea/",
      [".idea/test.xml", "contents"],
    ]);
    let result = await shouldIgnore(TEST_DIR + "/.env", testIde, [TEST_DIR]);
    expect(result).toBe(true);

    result = await shouldIgnore(TEST_DIR + "/go.sum", testIde, [TEST_DIR]);
    expect(result).toBe(true);

    result = await shouldIgnore(TEST_DIR + "/.idea", testIde, [TEST_DIR]);
    expect(result).toBe(true);

    result = await shouldIgnore(TEST_DIR + "/.idea/test.xml", testIde, [
      TEST_DIR,
    ]);
    expect(result).toBe(true);
  });

  test("should respect default file and folder ignores at nested level", async () => {
    addToTestDir([
      "nested/.idea/test.xml",
      ["nested/.env", "contents"],
      ["nested/go.sum", "contents"],
    ]);

    let result = await shouldIgnore(TEST_DIR + "/nested/.idea", testIde, [
      TEST_DIR,
    ]);
    expect(result).toBe(true);

    result = await shouldIgnore(TEST_DIR + "/nested/.idea/test.xml", testIde, [
      TEST_DIR,
    ]);
    expect(result).toBe(true);

    result = await shouldIgnore(TEST_DIR + "/nested/.env", testIde, [TEST_DIR]);
    expect(result).toBe(true);

    result = await shouldIgnore(TEST_DIR + "/nested/go.sum", testIde, [
      TEST_DIR,
    ]);
    expect(result).toBe(true);
  });
});
