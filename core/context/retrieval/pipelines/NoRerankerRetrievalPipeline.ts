import { Chunk } from "../../../";

import BaseRetrievalPipeline, {
  RetrievalPipelineRunArguments,
} from "./BaseRetrievalPipeline";

export default class NoRerankerRetrievalPipeline extends BaseRetrievalPipeline {
  async run(args: RetrievalPipelineRunArguments): Promise<Chunk[]> {
    const { nFinal } = this.options;

    // We give 1/4 weight to recently edited files, 1/4 to full text search,
    // and the remaining 1/2 to embeddings
    const recentlyEditedNFinal = nFinal * 0.25;
    const ftsNFinal = nFinal * 0.25;
    const embeddingsNFinal = nFinal - recentlyEditedNFinal - ftsNFinal;
    const retrievalResults = await this.performInitialRetrieval(
      args,
      ftsNFinal,
      embeddingsNFinal,
      recentlyEditedNFinal,
    );

    // 如果结果超过 nFinal，截取前 nFinal 个
    return retrievalResults.slice(-nFinal);
  }
}
